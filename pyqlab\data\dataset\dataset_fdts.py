import os
import pandas as pd
from pyqlab.data.dataset.dataset import Dataset

class Pipeline:
    def __init__(self, config):
        super().__init__(config)
        self.data = None
    
    def _load_data_from_file(self):
        pass

    def _preprocess_data(self):
        pass

    def get_data(self):
        self._load_data_from_file()
        self._preprocess_data()
        return self.data

class FdtsDataset(Dataset):

    def __init__(self, config):
        super().__init__(config)
        self.fd_set = config.fd_set
        self.sel_fd_names = config.sel_fd_names
        self.fd_dfs = {}
        self.data = None
    
    def _load_data_from_file(self):
        for fd in self.fd_set:
            for year in self.years:
                key = f'fd_{fd[0]}_{fd[1]}'
                data_file = f'{self.data_path}/ffs_{key}.{self.suffix}.{self.bar_size}.{year}.parquet'
                if not os.path.isfile(data_file):
                    print(f"{data_file} isn't exist.")
                    continue
                if key not in self.fd_dfs:
                    self.fd_dfs[key] = pd.DataFrame()
                self.fd_dfs[key] = pd.concat([self.fd_dfs[key], pd.read_parquet(data_file)])
                print(f"Loading {year} {key} {self.fd_dfs[key].shape}")
        